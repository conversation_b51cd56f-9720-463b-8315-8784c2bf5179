import ZAI from 'z-ai-web-dev-sdk';

export interface RoadmapGenerationInput {
  proficiencyLevel: string;
  learningGoals: string;
  timeCommitment: string;
}

export interface LessonContentInput {
  title: string;
  moduleContext: string;
  proficiencyLevel: string;
  previousLessons?: string[];
}

export interface ReviewContentInput {
  moduleTopics: string[];
  userProgress: number;
  difficulty: string;
}

export class AIService {
  private static instance: AIService;
  private zai: any;

  private constructor() {}

  static async getInstance(): Promise<AIService> {
    if (!AIService.instance) {
      AIService.instance = new AIService();
      await AIService.instance.initialize();
    }
    return AIService.instance;
  }

  private async initialize() {
    this.zai = await ZAI.create();
  }

  async generateRoadmapStructure(input: RoadmapGenerationInput): Promise<string> {
    const { proficiencyLevel, learningGoals, timeCommitment } = input;

    const prompt = `
Generate a Japanese learning roadmap for a ${proficiencyLevel} learner with the goal of "${learningGoals}" and ${timeCommitment} time commitment.

Follow these pedagogical principles:
1. Start with hiragana → katakana → basic grammar → kanji
2. Include review sessions every 3-5 modules
3. Adjust complexity based on goals (prioritize ${learningGoals.includes('conversation') ? 'conversation' : learningGoals.includes('writing') ? 'writing' : 'balanced skills'})
4. Each module should be 1-3 weeks long
5. Include 2-3 milestones per module
6. Include 3-5 lessons per module

Format your response as a structured text with the following format:

ROADMAP: [Roadmap Title]
DESCRIPTION: [Brief description]
DURATION: [Total duration in weeks]
STATUS: active

MODULE: 1
TITLE: [Module Title]
DESCRIPTION: [Module description]
DURATION: [Duration in weeks]
TYPE: [regular/review]

MILESTONE: 1
TITLE: [Milestone title]
DESCRIPTION: [Milestone description]

LESSON: 1
TITLE: [Lesson title]
CONTENT: [Lesson content placeholder - will be generated separately]
DURATION: [Duration in minutes]

[Continue with more milestones and lessons...]

[Continue with more modules...]

Make sure the roadmap is comprehensive and follows proper learning progression.
`;

    const response = await this.zai.chat.completions.create({
      messages: [
        {
          role: 'system',
          content: 'You are an expert Japanese language instructor and curriculum designer. Create structured, pedagogically sound learning roadmaps.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000,
    });

    return response.choices[0]?.message?.content || '';
  }

  async generateLessonContent(input: LessonContentInput): Promise<string> {
    const { title, moduleContext, proficiencyLevel, previousLessons = [] } = input;

    const prompt = `
Generate detailed content for a Japanese lesson titled "${title}".

Context:
- Module: ${moduleContext}
- Proficiency Level: ${proficiencyLevel}
- Previous Lessons: ${previousLessons.join(', ') || 'None'}

Please provide comprehensive lesson content that includes:
1. Japanese text with romaji pronunciation
2. English translation
3. Grammar explanations (if applicable)
4. Example sentences
5. Practice exercises
6. Cultural notes (if relevant)

Format the content in a clear, structured way that's easy to read and learn from.

Example format:
こんにちは (Konnichiwa) - Hello
This is a basic greeting used in the afternoon.

Grammar:
- こんにちは is used as a general greeting
- It's appropriate for most situations

Example Sentences:
1. こんにちは、元気ですか？ (Konnichiwa, genki desu ka?) - Hello, how are you?
2. こんにちは、田中さん (Konnichiwa, Tanaka-san) - Hello, Mr./Ms. Tanaka

Practice:
Try greeting different people using こんにちは.

Cultural Note:
In Japan, bowing slightly while saying こんにちは is considered polite.

Make the content engaging and educational for ${proficiencyLevel} learners.
`;

    const response = await this.zai.chat.completions.create({
      messages: [
        {
          role: 'system',
          content: 'You are an expert Japanese language teacher. Create detailed, engaging lesson content that helps students learn effectively.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 1500,
    });

    return response.choices[0]?.message?.content || '';
  }

  async generateReviewContent(input: ReviewContentInput): Promise<string> {
    const { moduleTopics, userProgress, difficulty } = input;

    const prompt = `
Generate a comprehensive review session for the following Japanese learning topics:

Topics to Review: ${moduleTopics.join(', ')}
User Progress: ${userProgress}%
Difficulty Level: ${difficulty}

Create a review session that:
1. Summarizes key concepts from each topic
2. Provides practice exercises that reinforce learning
3. Includes common mistakes and how to avoid them
4. Offers tips for better retention
5. Suggests additional practice resources

Format the content as a structured review session with clear sections and practical exercises.

The review should be challenging but achievable for someone with ${userProgress}% progress through the material.
`;

    const response = await this.zai.chat.completions.create({
      messages: [
        {
          role: 'system',
          content: 'You are an expert Japanese language instructor specializing in review and retention strategies.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.6,
      max_tokens: 1500,
    });

    return response.choices[0]?.message?.content || '';
  }

  async generateAdaptiveContent(
    baseContent: string,
    userPerformance: { score: number; timeSpent: number; mistakes: string[] }
  ): Promise<string> {
    const { score, timeSpent, mistakes } = userPerformance;

    const prompt = `
Adapt the following Japanese learning content based on user performance:

Original Content:
${baseContent}

User Performance:
- Score: ${score}%
- Time Spent: ${timeSpent} minutes
- Common Mistakes: ${mistakes.join(', ') || 'None'}

Please adapt the content by:
1. ${score < 70 ? 'Simplifying the explanations and adding more examples' : 'Adding more advanced concepts and challenges'}
2. ${timeSpent > 30 ? 'Making the content more concise and focused' : 'Adding more detailed explanations and practice'}
3. ${mistakes.length > 0 ? `Addressing these specific mistakes: ${mistakes.join(', ')}` : 'Maintaining the current difficulty level'}

Keep the core learning objectives the same but adjust the presentation and difficulty based on the user's performance.
`;

    const response = await this.zai.chat.completions.create({
      messages: [
        {
          role: 'system',
          content: 'You are an adaptive learning system that personalizes content based on user performance.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.6,
      max_tokens: 1500,
    });

    return response.choices[0]?.message?.content || baseContent;
  }

  async generateSpacedRepetitionContent(
    originalContent: string,
    daysSinceLastReview: number,
    previousPerformance: number
  ): Promise<string> {
    const prompt = `
Generate a spaced repetition review for the following Japanese content:

Original Content:
${originalContent}

Review Context:
- Days since last review: ${daysSinceLastReview}
- Previous performance score: ${previousPerformance}%

Create a review that:
1. ${daysSinceLastReview > 7 ? 'Provides a comprehensive refresher with key points' : 'Focuses on quick recall and reinforcement'}
2. ${previousPerformance < 80 ? 'Emphasizes areas that were previously challenging' : 'Introduces slight variations to test understanding'}
3. Includes new examples or contexts to reinforce learning
4. Provides immediate feedback mechanisms

The review should be optimized for long-term retention using spaced repetition principles.
`;

    const response = await this.zai.chat.completions.create({
      messages: [
        {
          role: 'system',
          content: 'You are an expert in spaced repetition learning systems for language acquisition.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.6,
      max_tokens: 1200,
    });

    return response.choices[0]?.message?.content || originalContent;
  }
}